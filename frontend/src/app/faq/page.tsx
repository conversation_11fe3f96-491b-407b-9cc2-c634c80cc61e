'use client';

import { FooterSection } from '@/components/home/<USER>/footer-section';
import AuroraBackground from '@/components/home/<USER>/AuroraBackground';
import Link from 'next/link';
import { ModalProviders } from '@/providers/modal-providers';
import { ChevronLeftIcon, PlusIcon, MinusIcon } from '@radix-ui/react-icons';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

type FAQItem = {
  id: number;
  question: string;
  answer: string;
};

export default function FAQPage() {
  const [openItem, setOpenItem] = useState<number | null>(null);

  const toggleItem = (id: number) => {
    setOpenItem(openItem === id ? null : id);
  };

  const faqItems: FAQItem[] = [
    {
      id: 1,
      question: "Qu'est-ce qu'Orchestra Connect et comment ça fonctionne ?",
      answer:
        "Orchestra Connect est une conciergerie d'affaires augmentée par l'intelligence artificielle, spécialement conçue pour les PME. Notre plateforme vous met en relation avec Alex, un agent IA avancé qui peut vous assister dans diverses tâches quotidiennes comme la recherche d'informations, l'analyse de données, la création de contenu, l'aide à la prospection, l'organisation, etc. Alex apprend de vos interactions pour devenir de plus en plus efficace et adapté à vos besoins spécifiques.",
    },
    {
      id: 2,
      question: "Quels sont les avantages d'utiliser Alex pour ma PME ?",
      answer:
        "Alex vous permet de gagner un temps précieux en automatisant les tâches répétitives et chronophages, ce qui vous permet de vous concentrer sur les aspects stratégiques de votre entreprise. Il améliore votre productivité, vous aide à prendre de meilleures décisions grâce à l'analyse de données, favorise l'innovation et vous donne accès à des compétences spécialisées sans avoir à embaucher de nouveaux talents. En résumé, Alex vous aide à faire plus avec moins, à optimiser vos ressources et à rester compétitif dans votre marché.",
    },
    {
      id: 3,
      question: 'Quels types de tâches Alex peut-il réaliser ?',
      answer:
        "Alex peut vous assister dans une grande variété de tâches : rédaction et optimisation de contenus (emails, articles de blog, publications pour réseaux sociaux), recherche et analyse d'informations, aide à la prospection commerciale, organisation et planification, analyse financière, support client, veille concurrentielle, création de présentations et de rapports, et bien plus encore. La polyvalence d'Alex en fait un outil adapté à presque tous les départements de votre entreprise.",
    },
    {
      id: 4,
      question:
        'Faut-il des compétences techniques pour utiliser Orchestra Connect ?',
      answer:
        "Absolument pas ! Orchestra Connect a été conçu pour être intuitif et accessible à tous, sans nécessiter de compétences techniques particulières. L'interface est simple et conviviale : il vous suffit d'expliquer ce dont vous avez besoin en langage naturel, et Alex comprend vos intentions pour vous fournir exactement ce que vous recherchez. Nous proposons également une courte formation pour vous aider à tirer le meilleur parti de notre plateforme.",
    },
    {
      id: 5,
      question: 'Comment Orchestra Connect protège-t-il mes données ?',
      answer:
        'La protection de vos données est notre priorité absolue. Orchestra Connect respecte scrupuleusement le RGPD et met en œuvre des mesures de sécurité robustes : chiffrement de bout en bout, hébergement sécurisé en Europe, accès strictement contrôlé, politiques de rétention des données claires, et audits de sécurité réguliers. Vos données ne sont jamais vendues à des tiers et sont uniquement utilisées pour vous fournir nos services. Vous gardez le contrôle total sur vos informations et pouvez demander leur suppression à tout moment.',
    },
    {
      id: 6,
      question: 'Quels sont les forfaits et tarifs proposés ?',
      answer:
        "Nous proposons plusieurs forfaits adaptés aux besoins des différentes tailles de PME. Notre forfait Essentiel commence à 49€/mois et comprend les fonctionnalités de base d'Alex. Le forfait Pro à 99€/mois inclut des fonctionnalités avancées et une utilisation illimitée. Pour les entreprises avec des besoins spécifiques, nous proposons également des forfaits personnalisés. Tous nos forfaits sont sans engagement et incluent une période d'essai gratuite de 14 jours pour vous permettre de tester Orchestra Connect sans risque.",
    },
    {
      id: 7,
      question: 'Puis-je connecter Orchestra Connect à mes outils existants ?',
      answer:
        "Oui, Orchestra Connect s'intègre avec de nombreux outils et plateformes couramment utilisés par les PME : Google Workspace, Microsoft 365, Slack, Trello, Asana, HubSpot, Salesforce, Notion, et bien d'autres. Ces intégrations permettent à Alex de travailler de manière transparente avec vos outils existants, augmentant ainsi son efficacité et sa valeur ajoutée. Si vous utilisez un outil spécifique non listé, contactez-nous pour discuter des possibilités d'intégration.",
    },
    {
      id: 8,
      question: "Quel support est disponible si j'ai besoin d'aide ?",
      answer:
        "Nous offrons plusieurs niveaux de support pour assurer votre réussite avec Orchestra Connect. Tous nos clients ont accès à une base de connaissances complète, des tutoriels vidéo, et un support par email avec un temps de réponse garanti sous 24h. Les forfaits Pro et Entreprise bénéficient également d'un support prioritaire avec des temps de réponse plus courts et d'un accès à des sessions de formation personnalisées. Notre équipe d'experts est là pour vous aider à chaque étape de votre parcours avec Orchestra Connect.",
    },
    {
      id: 9,
      question:
        "Orchestra Connect peut-il s'adapter à mon secteur d'activité spécifique ?",
      answer:
        "Absolument ! Alex est conçu pour s'adapter à une grande variété de secteurs d'activité. Que vous soyez dans le commerce, les services, la santé, l'éducation, la construction, ou tout autre domaine, Alex peut être personnalisé pour comprendre les spécificités de votre industrie et vous fournir une assistance pertinente. Au fur et à mesure de vos interactions, il apprend votre vocabulaire professionnel, vos processus et vos besoins spécifiques pour devenir de plus en plus efficace dans votre contexte particulier.",
    },
    {
      id: 10,
      question: 'Comment démarrer avec Orchestra Connect ?',
      answer:
        "Démarrer avec Orchestra Connect est simple et rapide. Il vous suffit de vous inscrire sur notre site pour créer votre compte et accéder à votre période d'essai gratuite. Vous pourrez immédiatement commencer à interagir avec Alex. Nous vous recommandons de commencer par des tâches simples pour vous familiariser avec la plateforme, puis d'explorer progressivement ses capacités plus avancées. Notre équipe d'onboarding vous contactera également pour vous offrir une session de présentation personnalisée et répondre à toutes vos questions.",
    },
  ];

  return (
    <>
      <AuroraBackground />
      <ModalProviders />
      <main className="flex flex-col items-center justify-center min-h-screen w-full">
        <div className="w-full max-w-4xl mx-auto pt-24 px-4 sm:px-6 pb-16">
          <div className="flex items-center mb-6">
            <Link
              href="/"
              className="flex items-center text-primary hover:underline"
            >
              <ChevronLeftIcon className="mr-1" />
              Retour à l&apos;accueil
            </Link>
          </div>

          <h1 className="text-4xl font-bold mb-6 text-center">
            Questions fréquentes
          </h1>

          <div className="text-center mb-12">
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Retrouvez les réponses aux questions les plus fréquemment posées
              sur Orchestra Connect et notre agent IA Alex.
            </p>
          </div>

          <div className="bg-card/30 backdrop-blur-sm border border-border rounded-xl p-2 mb-12">
            {faqItems.map((item) => (
              <div key={item.id} className="mb-2 last:mb-0">
                <button
                  onClick={() => toggleItem(item.id)}
                  className={`w-full flex items-center justify-between p-4 rounded-lg transition-colors ${
                    openItem === item.id
                      ? 'bg-primary/10 text-primary'
                      : 'hover:bg-muted/50'
                  }`}
                >
                  <span className="font-medium text-left">{item.question}</span>
                  <span className="ml-4 flex-shrink-0">
                    {openItem === item.id ? (
                      <MinusIcon className="h-5 w-5" />
                    ) : (
                      <PlusIcon className="h-5 w-5" />
                    )}
                  </span>
                </button>
                <AnimatePresence>
                  {openItem === item.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="p-4 pt-0 pl-8 text-muted-foreground">
                        {item.answer}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>

          <div className="bg-card border border-border rounded-xl p-8 text-center">
            <h2 className="text-2xl font-semibold mb-4">
              Vous avez d&apos;autres questions ?
            </h2>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Notre équipe est là pour vous aider. N&apos;hésitez pas à nous
              contacter directement pour toute question spécifique ou pour en
              savoir plus sur Orchestra Connect.
            </p>
            <Link
              href="/contact"
              className="bg-primary text-white font-medium px-6 py-3 rounded-md inline-block hover:bg-primary/90 transition-colors"
            >
              Contactez-nous
            </Link>
          </div>
        </div>

        <FooterSection />
      </main>
    </>
  );
}
