'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function LegalPage() {
  const router = useRouter();

  useEffect(() => {
    // Rediriger vers la page mentions légales par défaut
    router.replace('/legal/mentions-legales');
  }, [router]);

  return (
    <main className="flex flex-col items-center justify-center min-h-screen w-full">
      <div className="flex items-center justify-center min-h-screen">
        <p>Redirection en cours...</p>
      </div>
    </main>
  );
}
