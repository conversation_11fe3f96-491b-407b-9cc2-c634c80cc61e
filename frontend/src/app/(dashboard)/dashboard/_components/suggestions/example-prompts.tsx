'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BarChart3, Bot, Briefcase, Settings, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

type PromptExample = {
  title: string;
  description: string;
  query: string;
};

type CategoryData = {
  [key: string]: {
    title: string;
    icon: React.ReactNode;
    color: string;
    examples: PromptExample[];
  };
};

const categoryData: CategoryData = {
  data: {
    title: 'Analyse de données & Recherche',
    icon: <BarChart3 size={14} />,
    color: 'bg-blue-500 dark:bg-blue-600',
    examples: [
      {
        title: 'Tableau de recherche marché',
        description: 'Analyse de marché complète et insights',
        query:
          'Créez un tableau de bord complet d\'étude de marché analysant les tendances sectorielles, les segments clients et le paysage concurrentiel. Incluez la visualisation de données et des recommandations actionnables.',
      },
      {
        title: 'Analyse données sondage',
        description: 'Analyse statistique des réponses de sondage',
        query:
          'Analysez les réponses de sondage pour identifier les patterns clés, la signification statistique et les insights clients. Créez des rapports visuels avec des recommandations pour les décisions business.',
      },
      {
        title: 'Reporting business intelligence',
        description: 'Suivi KPI et analyse de performance',
        query:
          'Construisez un rapport de business intelligence suivant les indicateurs de performance clés. Incluez l\'analyse de tendances, prévisions et alertes automatisées pour les changements significatifs.',
      },
      {
        title: 'Analyse prix concurrents',
        description: 'Recherche stratégie de prix concurrentielle',
        query:
          'Menez une analyse des prix concurrents sur plusieurs produits et marchés. Identifiez les stratégies de prix, positionnement marché et opportunités d\'optimisation.',
      },
      {
        title: 'Analyse segmentation client',
        description: 'Profilage client et ciblage basé sur les données',
        query:
          'Effectuez une analyse de segmentation client utilisant des données comportementales et démographiques. Créez des personas détaillées et recommandations de ciblage pour les campagnes marketing.',
      },
      {
        title: 'Tableau de bord performance ventes',
        description: 'Suivi revenus et analytics ventes',
        query:
          'Créez un tableau de bord de performance des ventes avec métriques temps réel, analyse d\'entonnoir de conversion et prévisions prédictives pour la planification des revenus.',
      },
      {
        title: 'Analyse approfondie analytics web',
        description: 'Comportement utilisateur et optimisation conversion',
        query:
          'Analysez les données d\'analytics web pour identifier les patterns de comportement utilisateur, goulots d\'étranglement de conversion et opportunités d\'optimisation pour améliorer les performances.',
      },
      {
        title: 'Analyse tendances financières',
        description: 'Modélisation données financières et prévisions',
        query:
          'Analysez les tendances financières et créez des modèles de prévision pour la budgétisation et planification stratégique. Incluez l\'analyse de scénarios et évaluation des risques.',
      },
    ],
  },
  ai: {
    title: 'IA & Machine Learning',
    icon: <Bot size={14} />,
    color: 'bg-purple-500 dark:bg-purple-600',
    examples: [
      {
        title: 'Développement moteur de recommandation',
        description: 'Recommandations personnalisées de contenu et produits',
        query:
          'Développez un moteur de recommandation pour des suggestions de produits personnalisées. Incluez le filtrage collaboratif, filtrage basé sur le contenu et approches hybrides avec métriques d\'évaluation.',
      },
      {
        title: 'Outil traitement langage naturel',
        description: 'Analyse de texte et détection de sentiment',
        query:
          'Créez un outil NLP pour analyser les retours clients et le sentiment des réseaux sociaux. Incluez le préprocessing, classification et visualisation des insights.',
      },
      {
        title: 'Application vision par ordinateur',
        description: 'Reconnaissance d\'images et automatisation du traitement',
        query:
          'Construisez une application de vision par ordinateur pour la classification automatisée d\'images et détection d\'objets. Incluez l\'entraînement de modèle, optimisation et directives de déploiement.',
      },
      {
        title: 'Système détection de fraude',
        description: 'Détection d\'anomalies pour transactions financières',
        query:
          'Développez un système de détection de fraude utilisant des algorithmes de machine learning. Incluez l\'ingénierie de features, sélection de modèle, scoring temps réel et mécanismes d\'alerte.',
      },
      {
        title: 'Modèle prévision de demande',
        description: 'Analytics prédictives pour gestion d\'inventaire',
        query:
          'Créez un modèle de prévision de demande pour l\'optimisation d\'inventaire. Incluez l\'analyse de séries temporelles, ajustements saisonniers et évaluation d\'impact business.',
      },
      {
        title: 'Génération de contenu alimentée par IA',
        description: 'Création et optimisation automatisées de contenu',
        query:
          'Construisez un système IA pour la génération et optimisation automatisées de contenu. Incluez le contrôle qualité, cohérence de marque et suivi de performance.',
      },
      {
        title: 'Intégration assistant vocal',
        description: 'Reconnaissance vocale et compréhension langage naturel',
        query:
          'Développez une intégration d\'assistant vocal pour le service client. Incluez speech-to-text, reconnaissance d\'intention et capacités de génération de réponse.',
      },
      {
        title: 'Système maintenance prédictive',
        description: 'Analyse données IoT pour monitoring d\'équipement',
        query:
          'Créez un système de maintenance prédictive utilisant des données de capteurs IoT. Incluez la détection d\'anomalies, prédiction de pannes et optimisation de planification de maintenance.',
      },
    ],
  },
  business: {
    title: 'Business & Strategy',
    icon: <Briefcase size={14} />,
    color: 'bg-emerald-500 dark:bg-emerald-600',
    examples: [
      {
        title: 'Go-to-market strategy',
        description: 'Product launch and market entry planning',
        query:
          'Develop a comprehensive go-to-market strategy for a new product. Include market sizing, customer acquisition channels, pricing strategy, and launch timeline.',
      },
      {
        title: 'Brand positioning framework',
        description: 'Brand identity and competitive differentiation',
        query:
          'Create a brand positioning framework that differentiates from competitors. Include brand architecture, messaging hierarchy, and implementation guidelines.',
      },
      {
        title: 'Growth hacking playbook',
        description: 'Scalable user acquisition and retention strategies',
        query:
          'Design a growth hacking playbook with experimental frameworks, user acquisition funnels, retention strategies, and measurable growth metrics.',
      },
      {
        title: 'Strategic partnership analysis',
        description: 'Partnership evaluation and negotiation strategy',
        query:
          'Analyze potential strategic partnerships and create evaluation criteria. Include partnership models, negotiation strategies, and success metrics.',
      },
      {
        title: 'Digital transformation roadmap',
        description: 'Technology adoption and organizational change',
        query:
          'Create a digital transformation roadmap for organizational modernization. Include technology assessment, change management, and implementation phases.',
      },
      {
        title: 'Pricing optimization strategy',
        description: 'Revenue maximization through pricing models',
        query:
          'Develop a pricing optimization strategy using market research and competitor analysis. Include dynamic pricing models and revenue impact projections.',
      },
      {
        title: 'Customer retention program',
        description: 'Loyalty and engagement strategy development',
        query:
          'Design a comprehensive customer retention program with loyalty incentives, engagement strategies, and churn reduction tactics.',
      },
      {
        title: 'Market expansion analysis',
        description: 'New market opportunity assessment',
        query:
          'Conduct market expansion analysis for entering new geographic or demographic markets. Include risk assessment, resource requirements, and entry strategies.',
      },
    ],
  },
  automation: {
    title: 'Automation & Scripts',
    icon: <Settings size={14} />,
    color: 'bg-orange-500 dark:bg-orange-600',
    examples: [
      {
        title: 'Data pipeline automation',
        description: 'ETL processes and data workflow management',
        query:
          'Create an automated data pipeline for ETL processes. Include data validation, error handling, monitoring, and scalable architecture design.',
      },
      {
        title: 'Email marketing automation',
        description: 'Automated email campaigns and nurture sequences',
        query:
          'Build an email marketing automation system with segmentation, personalization, A/B testing, and performance tracking capabilities.',
      },
      {
        title: 'Social media scheduling bot',
        description: 'Automated content publishing and engagement',
        query:
          'Develop a social media scheduling bot for automated content publishing. Include multi-platform support, optimal timing, and engagement tracking.',
      },
      {
        title: 'Invoice processing automation',
        description: 'Automated invoice generation and management',
        query:
          'Create an automated invoice processing system with OCR, data extraction, approval workflows, and integration with accounting systems.',
      },
      {
        title: 'Lead qualification automation',
        description: 'Automated lead scoring and routing system',
        query:
          'Build a lead qualification automation system with scoring algorithms, routing rules, and CRM integration for sales team efficiency.',
      },
      {
        title: 'Inventory management automation',
        description: 'Stock monitoring and reorder automation',
        query:
          'Develop an inventory management automation system with real-time tracking, automatic reordering, and supplier integration.',
      },
      {
        title: 'Report generation automation',
        description: 'Automated business reporting and distribution',
        query:
          'Create an automated report generation system with scheduled delivery, customizable templates, and stakeholder-specific formatting.',
      },
      {
        title: 'Quality assurance automation',
        description: 'Automated testing and quality control processes',
        query:
          'Build a quality assurance automation framework with test case generation, execution scheduling, and results reporting.',
      },
    ],
  },
  emerging: {
    title: 'Emerging Use Cases',
    icon: <TrendingUp size={14} />,
    color: 'bg-indigo-500 dark:bg-indigo-600',
    examples: [
      {
        title: 'Real estate investment analysis',
        description: 'Property valuation and market opportunity assessment',
        query:
          'Analyze real estate listings to identify high-value investment opportunities. Include market trends, ROI calculations, risk assessment, and portfolio recommendations.',
      },
      {
        title: 'Trading algorithm development',
        description: 'Automated trading strategies and backtesting',
        query:
          'Develop a trading algorithm with backtesting capabilities. Include technical indicators, risk management, portfolio optimization, and performance analysis.',
      },
      {
        title: 'Content creation suite',
        description: 'Multi-format content generation and optimization',
        query:
          'Create a comprehensive content creation suite for blogs, social media, and presentations. Include SEO optimization, brand consistency, and performance tracking.',
      },
      {
        title: 'Property management automation',
        description: 'Tenant communication and maintenance scheduling',
        query:
          'Build a property management automation system with tenant portals, maintenance scheduling, rent collection, and financial reporting.',
      },
      {
        title: 'Portfolio management dashboard',
        description: 'Investment tracking and risk analysis',
        query:
          'Create a portfolio management dashboard with real-time tracking, risk analysis, performance attribution, and rebalancing recommendations.',
      },
      {
        title: 'Presentation automation tool',
        description: 'Dynamic slide generation and design optimization',
        query:
          'Develop a presentation automation tool that generates slides from data inputs. Include template management, design optimization, and collaboration features.',
      },
      {
        title: 'Market sentiment analyzer',
        description: 'News and social media sentiment tracking',
        query:
          'Build a market sentiment analyzer using news and social media data. Include sentiment scoring, trend identification, and investment signal generation.',
      },
      {
        title: 'Documentation generator',
        description: 'Automated technical and business documentation',
        query:
          'Create an automated documentation generator for technical and business processes. Include template management, version control, and collaboration workflows.',
      },
    ],
  },
};

export const ExamplePrompts = ({
  onSelectPrompt,
}: {
  onSelectPrompt?: (query: string) => void;
}) => {
  const [activeCategory, setActiveCategory] = useState('data');
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    setIsTransitioning(true);
    const timer = setTimeout(() => setIsTransitioning(false), 300);
    return () => clearTimeout(timer);
  }, [activeCategory]);

  return (
    <div className="w-full max-w-6xl mx-auto px-2">
      <motion.div
        initial={{ y: 0, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="flex flex-wrap gap-2 mb-6 justify-center"
      >
        {Object.keys(categoryData).map((category) => (
          <motion.div key={category}>
            <Button
              variant={activeCategory === category ? 'default' : 'outline'}
              className={cn(
                'text-sm rounded-full transition-all duration-300 relative overflow-hidden',
                activeCategory === category ? 'px-5' : 'px-4',
                'flex items-center gap-1.5',
              )}
              size="sm"
              onClick={() => {
                if (activeCategory !== category) {
                  setActiveCategory(category);
                }
              }}
            >
              {activeCategory === category && (
                <motion.div
                  layoutId="activeCategoryBackground"
                  className={cn(
                    'absolute inset-0 opacity-90 dark:opacity-80 rounded-full bg-primary',
                  )}
                  initial={{ scale: 1, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.2 }}
                />
              )}
              <span className="relative z-10">
                {categoryData[category].icon}
              </span>
              <span className="relative z-10">
                {categoryData[category].title}
              </span>
            </Button>
          </motion.div>
        ))}
      </motion.div>

      <motion.div
        className="mt-4 mb-12"
        initial={false}
        animate={{ opacity: isTransitioning ? 0.5 : 1 }}
        transition={{ duration: 0.2 }}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
          {categoryData[activeCategory].examples.map((example, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 0 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                delay: index * 0.05,
                duration: 0.3,
                type: 'spring',
                stiffness: 100,
              }}
            >
              <Card
                className={cn(
                  'cursor-pointer h-full shadow-none transition-all duration-300 bg-muted/50 dark:bg-card/50 relative overflow-hidden group',
                  hoveredCard === index
                    ? 'border-primary/70 dark:border-primary/70'
                    : 'hover:border-primary/40',
                )}
                onClick={() => onSelectPrompt && onSelectPrompt(example.query)}
                onMouseEnter={() => setHoveredCard(index)}
                onMouseLeave={() => setHoveredCard(null)}
              >
                {hoveredCard === index && (
                  <motion.div
                    className="absolute inset-0 bg-primary/5 dark:bg-primary/10 pointer-events-none"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  />
                )}
                <CardHeader className="px-4 relative">
                  <motion.div
                    className={cn(
                      'w-8 h-8 p-2 rounded-full flex items-center justify-center bg-primary',
                    )}
                    whileHover={{ rotate: 10 }}
                    transition={{ type: 'spring', stiffness: 300, damping: 10 }}
                  >
                    <div className="text-background">
                      {categoryData[activeCategory].icon}
                    </div>
                  </motion.div>
                  <div className="flex items-start justify-between mb-1">
                    <CardTitle
                      className={cn('text-md font-semibold text-foreground')}
                    >
                      {example.title}
                    </CardTitle>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {example.description}
                  </p>
                </CardHeader>
                <CardContent className="px-4 pb-4 pt-0">
                  <p className="text-xs text-muted-foreground/80 line-clamp-2 group-hover:text-muted-foreground transition-colors duration-200">
                    {example.query}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};
