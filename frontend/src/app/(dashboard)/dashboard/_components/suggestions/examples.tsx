'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  BarChart3,
  Bot,
  Briefcase,
  Settings,
  Sparkles,
  RefreshCw,
  TrendingUp,
  Users,
  Shield,
  Zap,
  Target,
  Brain,
  Globe,
  Heart,
  PenTool,
  Code,
  Camera,
  Calendar,
  DollarSign,
  Rocket,
} from 'lucide-react';

type PromptExample = {
  title: string;
  query: string;
  icon: React.ReactNode;
};

const allPrompts: PromptExample[] = [
  {
    title: 'Tableau de recherche marché',
    query:
      'Créez un tableau de bord complet d\'étude de marché analysant les tendances sectorielles, les segments clients et le paysage concurrentiel. Incluez la visualisation de données et des recommandations actionnables.',
    icon: (
      <BarChart3 className="text-green-700 dark:text-green-400" size={16} />
    ),
  },
  {
    title: 'Moteur de recommandation',
    query:
      'Développez un moteur de recommandation pour des suggestions de produits personnalisées. Incluez le filtrage collaboratif, le filtrage basé sur le contenu et des approches hybrides avec des métriques d\'évaluation.',
    icon: <Bot className="text-blue-700 dark:text-blue-400" size={16} />,
  },
  {
    title: 'Stratégie de lancement',
    query:
      'Développez une stratégie complète de mise sur le marché pour un nouveau produit. Incluez la taille du marché, les canaux d\'acquisition client, la stratégie de prix et le calendrier de lancement.',
    icon: <Briefcase className="text-rose-700 dark:text-rose-400" size={16} />,
  },
  {
    title: 'Automatisation pipeline données',
    query:
      'Créez un pipeline de données automatisé pour les processus ETL. Incluez la validation des données, la gestion d\'erreurs, la surveillance et la conception d\'architecture évolutive.',
    icon: (
      <Settings className="text-purple-700 dark:text-purple-400" size={16} />
    ),
  },
  {
    title: 'Système de productivité',
    query:
      'Concevez un système de productivité personnelle complet incluant la gestion des tâches, le suivi des objectifs, la formation d\'habitudes et le blocage de temps. Créez des modèles et workflows pour la planification quotidienne, hebdomadaire et mensuelle.',
    icon: <Target className="text-orange-700 dark:text-orange-400" size={16} />,
  },
  {
    title: 'Plan marketing de contenu',
    query:
      'Développez une stratégie de marketing de contenu sur 6 mois incluant articles de blog, réseaux sociaux, campagnes email et optimisation SEO. Incluez un calendrier de contenu et des métriques de performance.',
    icon: (
      <PenTool className="text-indigo-700 dark:text-indigo-400" size={16} />
    ),
  },
  {
    title: 'Analyse de portefeuille',
    query:
      'Créez un outil d\'analyse de portefeuille d\'investissement personnel avec évaluation des risques, recommandations de diversification et suivi des performances par rapport aux indices de référence.',
    icon: (
      <DollarSign
        className="text-emerald-700 dark:text-emerald-400"
        size={16}
      />
    ),
  },
  {
    title: 'Parcours client',
    query:
      'Cartographiez le parcours client complet de la sensibilisation à la recommandation. Incluez les points de contact, points de friction, émotions et opportunités d\'optimisation à chaque étape.',
    icon: <Users className="text-cyan-700 dark:text-cyan-400" size={16} />,
  },
  {
    title: 'Framework de tests A/B',
    query:
      'Concevez un framework complet de tests A/B incluant la formation d\'hypothèses, les calculs de signification statistique et les directives d\'interprétation des résultats.',
    icon: <TrendingUp className="text-teal-700 dark:text-teal-400" size={16} />,
  },
  {
    title: 'Automatisation revue de code',
    query:
      'Créez un système automatisé de revue de code qui vérifie les vulnérabilités de sécurité, les problèmes de performance et les standards de codage. Incluez l\'intégration avec les pipelines CI/CD.',
    icon: <Code className="text-violet-700 dark:text-violet-400" size={16} />,
  },
  {
    title: 'Matrice d\'évaluation des risques',
    query:
      'Développez un framework complet d\'évaluation des risques pour les opérations commerciales incluant l\'identification des risques, l\'analyse de probabilité, l\'évaluation d\'impact et les stratégies d\'atténuation.',
    icon: <Shield className="text-red-700 dark:text-red-400" size={16} />,
  },
  {
    title: 'Générateur de parcours d\'apprentissage',
    query:
      'Créez un générateur de parcours d\'apprentissage personnalisé qui s\'adapte aux objectifs individuels, au niveau de compétence actuel et au style d\'apprentissage préféré. Incluez le suivi des progrès et les recommandations de ressources.',
    icon: <Brain className="text-pink-700 dark:text-pink-400" size={16} />,
  },
  {
    title: 'Automatisation réseaux sociaux',
    query:
      'Concevez un système d\'automatisation des réseaux sociaux incluant la programmation de contenu, le suivi d\'engagement, l\'optimisation des hashtags et l\'analyse de performance sur plusieurs plateformes.',
    icon: <Globe className="text-blue-600 dark:text-blue-300" size={16} />,
  },
  {
    title: 'Tableau de bord santé',
    query:
      'Construisez un tableau de bord complet de suivi de santé intégrant les données de fitness, la journalisation nutritionnelle, les patterns de sommeil et les dossiers médicaux avec des insights actionnables et la définition d\'objectifs.',
    icon: <Heart className="text-red-600 dark:text-red-300" size={16} />,
  },
  {
    title: 'Automatisation de projet',
    query:
      'Créez un système intelligent de gestion de projet avec attribution automatique des tâches, suivi des échéances, allocation des ressources et intégration de communication d\'équipe.',
    icon: <Calendar className="text-amber-700 dark:text-amber-400" size={16} />,
  },
  {
    title: 'Optimiseur d\'entonnoir de vente',
    query:
      'Analysez et optimisez l\'entonnoir de vente complet de la génération de leads à la conversion. Incluez le scoring de leads, les séquences de nurturing et les stratégies d\'optimisation du taux de conversion.',
    icon: <Zap className="text-yellow-600 dark:text-yellow-300" size={16} />,
  },
  {
    title: 'Pitch deck startup',
    query:
      'Générez un pitch deck startup convaincant incluant l\'énoncé du problème, l\'aperçu de la solution, l\'analyse de marché, le modèle d\'affaires, les projections financières et les besoins de financement.',
    icon: <Rocket className="text-orange-600 dark:text-orange-300" size={16} />,
  },
  {
    title: 'Workflow photographie',
    query:
      'Concevez un workflow de photographie de bout en bout incluant la planification de shooting, l\'organisation des fichiers, les presets d\'édition, la livraison client et les systèmes de gestion de portfolio.',
    icon: <Camera className="text-slate-700 dark:text-slate-400" size={16} />,
  },
  {
    title: 'Analyse chaîne d\'approvisionnement',
    query:
      'Créez une analyse d\'optimisation de la chaîne d\'approvisionnement incluant l\'évaluation des fournisseurs, les opportunités de réduction des coûts, l\'atténuation des risques et les stratégies de gestion d\'inventaire.',
    icon: (
      <Briefcase className="text-stone-700 dark:text-stone-400" size={16} />
    ),
  },
  {
    title: 'Framework recherche UX',
    query:
      'Développez un framework complet de recherche UX incluant les entretiens utilisateurs, les tests d\'utilisabilité, le développement de personas et les recommandations de design basées sur les données.',
    icon: (
      <Sparkles className="text-fuchsia-700 dark:text-fuchsia-400" size={16} />
    ),
  },
];

// Function to get random prompts
const getRandomPrompts = (count: number = 3): PromptExample[] => {
  const shuffled = [...allPrompts].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const Examples = ({
  onSelectPrompt,
}: {
  onSelectPrompt?: (query: string) => void;
}) => {
  const [displayedPrompts, setDisplayedPrompts] = useState<PromptExample[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Initialize with random prompts on mount
  useEffect(() => {
    setDisplayedPrompts(getRandomPrompts(3));
  }, []);

  const handleRefresh = () => {
    setIsRefreshing(true);
    setDisplayedPrompts(getRandomPrompts(3));
    setTimeout(() => setIsRefreshing(false), 500);
  };

  return (
    <div className="w-full max-w-3xl mx-auto px-4">
      <div className="flex justify-between items-center mb-3">
        <span className="text-xs text-muted-foreground font-medium">
          Démarrage rapide
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
        >
          <motion.div
            animate={{ rotate: isRefreshing ? 360 : 0 }}
            transition={{ duration: 0.5, ease: 'easeInOut' }}
          >
            <RefreshCw size={10} />
          </motion.div>
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {displayedPrompts.map((prompt, index) => (
          <motion.div
            key={`${prompt.title}-${index}`}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{
              duration: 0.3,
              delay: index * 0.05,
              ease: 'easeOut',
            }}
          >
            <Card
              className="group cursor-pointer h-full shadow-none transition-all bg-sidebar hover:bg-neutral-100 dark:hover:bg-neutral-800/60 p-0 justify-center"
              onClick={() => onSelectPrompt && onSelectPrompt(prompt.query)}
            >
              <CardHeader className="p-2 grid-rows-1">
                <div className="flex items-start justify-center gap-1.5">
                  <div className="flex-shrink-0 mt-0.5">
                    {React.cloneElement(prompt.icon as React.ReactElement, {
                      size: 14,
                    })}
                  </div>
                  <CardTitle className="font-normal group-hover:text-foreground transition-all text-muted-foreground text-xs leading-relaxed line-clamp-3">
                    {prompt.title}
                  </CardTitle>
                </div>
              </CardHeader>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};
