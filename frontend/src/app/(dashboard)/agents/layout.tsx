import { agentPlaygroundFlagFrontend } from '@/flags';
import { isFlagEnabled } from '@/lib/feature-flags';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Conversation Agent | Orchestra Connect',
  description: 'Conversation agent interactive alimentée par Orchestra Connect',
  openGraph: {
    title: 'Conversation Agent | Orchestra Connect',
    description: 'Conversation agent interactive alimentée par Orchestra Connect',
    type: 'website',
  },
};

export default async function AgentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');
  if (!agentPlaygroundEnabled) {
    redirect('/dashboard');
  }
  return <>{children}</>;
}
