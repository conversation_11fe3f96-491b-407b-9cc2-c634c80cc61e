'use client';

import { useEffect, useState } from 'react';
import { CTASection } from '@/components/home/<USER>/cta-section';
// import { FAQSection } from "@/components/sections/faq-section";
import { FooterSection } from '@/components/home/<USER>/footer-section';
import { HeroSection } from '@/components/home/<USER>/hero-section';
import { OpenSourceSection } from '@/components/home/<USER>/open-source-section';
// import { PricingSectionFr } from '@/components/home/<USER>/pricing-section-fr';
import { PricingSectionFr } from '@/components/home/<USER>/pricing-section-fr';
import { UseCasesSection } from '@/components/home/<USER>/use-cases-section';
import { ModalProviders } from '@/providers/modal-providers';
import AuroraBackground from '@/components/home/<USER>/AuroraBackground';
import { DevModeBanner } from '@/components/home/<USER>/dev-mode-banner';

export default function Home() {
  return (
    <>
      <AuroraBackground />
      <ModalProviders />
      <main className="flex flex-col items-center justify-center min-h-screen w-full">
        <div className="w-full divide-y divide-border">
          <HeroSection />
          <UseCasesSection />
          {/* <CompanyShowcase /> */}
          {/* <BentoSection /> */}
          {/* <QuoteSection /> */}
          {/* <FeatureSection /> */}
          {/* <GrowthSection /> */}
          <OpenSourceSection />
          <PricingSectionFr />
          {/* <TestimonialSection /> */}
          {/* <FAQSection /> */}
          <CTASection />
          <FooterSection />
        </div>
      </main>
    </>
  );
}
