import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Zap } from 'lucide-react';
import { useModal } from '@/hooks/use-modal-store';
import { PricingSectionCompact } from './pricing-section-compact';

const returnUrl = process.env.NEXT_PUBLIC_URL || 'http://localhost:3000';

export const PaymentRequiredDialog = () => {
  const { isOpen, type, onClose } = useModal();
  const isModalOpen = isOpen && type === 'paymentRequiredDialog';

  return (
    <Dialog open={isModalOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-[600px] max-h-[85vh] overflow-hidden flex flex-col p-0">
        <DialogHeader className="px-6 pt-6 flex-shrink-0">
          <DialogTitle className="text-xl font-semibold">
            Limite d'utilisation atteinte
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            Vous avez épuisé votre forfait pour cette période. Passez à un forfait premium pour continuer à utiliser Alex.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 pb-4 overflow-y-auto scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent px-6 min-h-0">
          <div className="space-y-6 pb-4">
            <div className="flex items-start p-4 bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  <Zap className="w-5 h-5 text-amber-600 dark:text-amber-400" />
                </div>
                <div className="text-sm min-w-0">
                  <p className="font-medium text-amber-800 dark:text-amber-200">
                    Forfait épuisé
                  </p>
                  <p className="text-amber-700 dark:text-amber-300 break-words">
                    Votre forfait actuel a été entièrement utilisé pour cette période de facturation.
                  </p>
                </div>
              </div>
            </div>

            <div className="w-full">
              <PricingSectionCompact
                returnUrl={`${returnUrl}/dashboard`}
                hideFree={true}
              />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
