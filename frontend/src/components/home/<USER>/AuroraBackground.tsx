'use client';

import { useEffect, useRef, useState } from 'react';
import styles from './AuroraBackground.module.css';

export default function AuroraBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const rafRef = useRef<number>(0);
  const isVisibleRef = useRef<boolean>(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    const prefersReducedMotion = window.matchMedia(
      '(prefers-reduced-motion: reduce)',
    ).matches;
    const isMobile =
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent,
      );
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Démarrer immédiatement visible pour éviter tout retard
    isVisibleRef.current = true;

    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
    if (!gl) {
      console.error('WebGL not supported');
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        isVisibleRef.current = entries[0]?.isIntersecting ?? false;
      },
      { threshold: 0.1 },
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    function createShader(
      gl: WebGLRenderingContext,
      type: number,
      source: string,
    ) {
      const shader = gl.createShader(type);
      if (!shader) {
        throw new Error('Could not create shader');
      }
      gl.shaderSource(shader, source);
      gl.compileShader(shader);
      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error(gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
      }
      return shader;
    }

    function createProgram(
      gl: WebGLRenderingContext,
      vertexShader: WebGLShader,
      fragmentShader: WebGLShader,
    ) {
      const program = gl.createProgram();
      if (!program) {
        throw new Error('Could not create program');
      }
      gl.attachShader(program, vertexShader);
      gl.attachShader(program, fragmentShader);
      gl.linkProgram(program);
      if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error(gl.getProgramInfoLog(program));
        gl.deleteProgram(program);
        return null;
      }
      return program;
    }

    const vertexShaderSource = `
      attribute vec2 position;
      void main() { gl_Position = vec4(position, 0, 1); }
    `;

    const fragmentShaderSource = `
      precision highp float;
      uniform float iTime;
      uniform vec2 iResolution;
      #define filmGrainIntensity 0.1

      mat2 Rot(float a) {
          float s = sin(a);
          float c = cos(a);
          return mat2(c, -s, s, c);
      }

      vec2 hash(vec2 p) {
          p = vec2(dot(p, vec2(2127.1, 81.17)), dot(p, vec2(1269.5, 283.37)));
          return fract(sin(p)*43758.5453);
      }

      float noise(in vec2 p) {
          vec2 i = floor(p);
          vec2 f = fract(p);
          vec2 u = f*f*(3.0-2.0*f);
          float n = mix(mix(dot(-1.0+2.0*hash(i + vec2(0.0, 0.0)), f - vec2(0.0, 0.0)),
                          dot(-1.0+2.0*hash(i + vec2(1.0, 0.0)), f - vec2(1.0, 0.0)), u.x),
                     mix(dot(-1.0+2.0*hash(i + vec2(0.0, 1.0)), f - vec2(0.0, 1.0)),
                          dot(-1.0+2.0*hash(i + vec2(1.0, 1.0)), f - vec2(1.0, 1.0)), u.x), u.y);
          return 0.5 + 0.5*n;
      }

      float filmGrainNoise(in vec2 uv) {
          return length(hash(vec2(uv.x, uv.y)));
      }

      void main() {
          vec2 fragCoord = gl_FragCoord.xy;
          vec2 uv = fragCoord / iResolution.xy;
          float aspectRatio = iResolution.x / iResolution.y;

          vec2 tuv = uv - .5;
          float degree = noise(vec2(iTime*.05, tuv.x*tuv.y));
          tuv.y *= 1./aspectRatio;
          tuv = Rot(radians((degree-.5)*720.+180.)) * tuv;
          tuv.y *= aspectRatio;

          float frequency = 5.;
          float amplitude = 30.;
          float speed = iTime * 2.;
          tuv.x += sin(tuv.y*frequency+speed)/amplitude;
          tuv.y += sin(tuv.x*frequency*1.5+speed)/(amplitude*.5);

          vec3 auroraGreen = vec3(80.0, 255.0, 130.0)/255.0;
          vec3 auroraCyan = vec3(70.0, 230.0, 255.0)/255.0;
          vec3 auroraMagenta = vec3(220.0, 100.0, 255.0)/255.0;
          vec3 auroraBlue = vec3(30.0, 60.0, 200.0)/255.0;
          vec3 auroraPurple = vec3(120.0, 60.0, 200.0)/255.0;
          vec3 auroraPink = vec3(255.0, 110.0, 180.0)/255.0;
          vec3 auroraYellowGreen = vec3(180.0, 255.0, 120.0)/255.0;
          vec3 auroraMidnight = vec3(10.0, 30.0, 60.0)/255.0;

          float cycle = sin(iTime * 0.5);
          float t = (sign(cycle) * pow(abs(cycle), 0.6) + 1.) / 2.;
          vec3 color1 = mix(auroraGreen, auroraPurple, t);
          vec3 color2 = mix(auroraCyan, auroraMidnight, t);
          vec3 color3 = mix(auroraMagenta, auroraYellowGreen, t);
          vec3 color4 = mix(auroraBlue, auroraPink, t);

          vec3 layer1 = mix(color3, color2, smoothstep(-.3, .2, (Rot(radians(-5.))*tuv).x));
          vec3 layer2 = mix(color4, color1, smoothstep(-.3, .2, (Rot(radians(-5.))*tuv).x));
          vec3 color = mix(layer1, layer2, smoothstep(.5, -.3, tuv.y));

          color = color - filmGrainNoise(uv) * filmGrainIntensity;

          gl_FragColor = vec4(color, 1.0);
      }
    `;

    const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = createShader(
      gl,
      gl.FRAGMENT_SHADER,
      fragmentShaderSource,
    );

    if (!vertexShader || !fragmentShader) return;
    const program = createProgram(gl, vertexShader, fragmentShader);
    if (!program) return;

    const positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    gl.bufferData(
      gl.ARRAY_BUFFER,
      new Float32Array([-1, -1, 1, -1, -1, 1, 1, 1]),
      gl.STATIC_DRAW,
    );

    const positionLocation = gl.getAttribLocation(program, 'position');
    const timeLocation = gl.getUniformLocation(program, 'iTime');
    const resolutionLocation = gl.getUniformLocation(program, 'iResolution');

    let lastFrameTime = 0;
    const targetFPS = isMobile ? 30 : 60;
    const frameInterval = 1000 / targetFPS;

    function resize() {
      const scale = isMobile ? 0.5 : 1.0;
      if (!canvas) return;
      canvas.width = window.innerWidth * scale;
      canvas.height = window.innerHeight * scale;
      canvas.style.width = `${window.innerWidth}px`;
      canvas.style.height = `${window.innerHeight}px`;
      gl.viewport(0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight);
    }

    function render(timestamp = 0) {
      if (isVisibleRef.current && timestamp - lastFrameTime >= frameInterval) {
        lastFrameTime = timestamp;
        gl.useProgram(program);
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);
        gl.uniform1f(timeLocation, timestamp * 0.001);
        if (!canvas) return;
        gl.uniform2f(resolutionLocation, canvas.width, canvas.height);
        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
      }
      if (prefersReducedMotion) {
        rafRef.current = requestAnimationFrame((t) => render(t * 0.5));
      } else {
        rafRef.current = requestAnimationFrame(render);
      }
    }

    const debouncedResize = debounce(resize, 100); // Réduction du délai

    // Initialisation immédiate
    resize();
    window.addEventListener('resize', debouncedResize);

    // Démarrer l'animation immédiatement sans condition
    rafRef.current = requestAnimationFrame(render);

    return () => {
      observer.disconnect();
      window.removeEventListener('resize', debouncedResize);
      cancelAnimationFrame(rafRef.current);
      if (gl) {
        if (program) gl.deleteProgram(program);
        if (vertexShader) gl.deleteShader(vertexShader);
        if (fragmentShader) gl.deleteShader(fragmentShader);
        if (positionBuffer) gl.deleteBuffer(positionBuffer);
      }
    };
  }, [isClient]);

  function debounce(func: (...args: any[]) => void, wait: number) {
    let timeout: NodeJS.Timeout;
    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  if (!isClient) {
    return null;
  }

  return (
    <div className={styles.auroraContainer} ref={containerRef}>
      <canvas ref={canvasRef} className={styles.canvas} />
      <div className={styles.overlay} />
    </div>
  );
}
