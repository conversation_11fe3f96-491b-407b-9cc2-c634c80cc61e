import React from 'react';
import Link from 'next/link';

export function DevModeBanner() {
  return (
    <div className="w-full bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-sm border-y border-border">
      <div className="max-w-7xl mx-auto py-3 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-wrap items-center justify-center sm:justify-between gap-2">
          <div className="flex items-center gap-2 flex-wrap justify-center">
            <span className="text-sm font-medium text-primary px-2 py-1 rounded-full bg-primary/5">
              Forfait Gratuit
            </span>
            <span className="text-muted-foreground text-sm">•</span>
            <span className="text-sm font-medium text-primary px-2 py-1 rounded-full bg-primary/5">
              59,99€/mois
            </span>
            <span className="text-muted-foreground text-sm">•</span>
            <span className="text-sm font-medium text-primary px-2 py-1 rounded-full bg-primary/5">
              299€/mois
            </span>
            <span className="text-muted-foreground text-sm">•</span>
            <span className="text-sm font-medium text-primary px-2 py-1 rounded-full bg-primary/5">
              599€/mois
            </span>
          </div>
          <Link
            href="#pricing"
            className="inline-flex items-center justify-center text-xs font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow hover:from-green-600 hover:to-emerald-700 h-8 rounded-full px-4"
          >
            Voir les tarifs
          </Link>
        </div>
      </div>
    </div>
  );
}
