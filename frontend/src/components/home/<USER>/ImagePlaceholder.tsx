'use client';

import React from 'react';

interface ImagePlaceholderProps {
  alt?: string;
  className?: string;
  style?: React.CSSProperties;
}

export default function ImagePlaceholder({
  alt = 'Image',
  className = '',
  style = {},
}: ImagePlaceholderProps) {
  return (
    <div
      className={`${className} bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center`}
      style={style}
    >
      <p className="text-primary-foreground text-sm font-medium p-2 bg-black/30 rounded backdrop-blur-sm">
        {alt}
      </p>
    </div>
  );
}
