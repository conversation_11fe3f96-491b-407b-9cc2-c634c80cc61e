'use client';

import { SectionHeader } from '@/components/home/<USER>';
import { cn } from '@/lib/utils';
import { CheckIcon } from 'lucide-react';
import Link from 'next/link';

export function PricingSectionFr() {
  return (
    <section
      id="pricing"
      className="flex flex-col items-center justify-center gap-10 w-full relative py-20"
    >
      <SectionHeader>
        <h2 className="text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance">
          Choisissez le forfait adapté à vos besoins
        </h2>
        <p className="text-muted-foreground text-center text-balance font-medium">
          Démarrez avec notre forfait gratuit ou passez à un forfait premium
          pour plus de missions
        </p>
      </SectionHeader>

      <div className="grid gap-6 w-full max-w-6xl mx-auto px-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        {/* Forfait Gratuit */}
        <div className="rounded-xl border border-white/10 backdrop-blur-md bg-card/70 p-6 flex flex-col h-full transition-all hover:bg-card/80 hover:shadow-[0_0_15px_rgba(0,214,143,0.15)]">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-1">Découverte</h3>
            <div className="flex items-baseline gap-1">
              <span className="text-3xl font-bold">Gratuit</span>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Idéal pour tester les fonctionnalités d'Alex
            </p>
          </div>

          <div className="space-y-4 flex-grow">
            <div className="bg-muted/40 rounded-lg p-3">
              <p className="text-sm font-medium">3 missions par mois</p>
              <p className="text-xs text-muted-foreground">
                Environ 30 minutes d'utilisation
              </p>
            </div>

            <ul className="space-y-2.5">
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">Accès à Alex (IA généraliste)</span>
              </li>
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">Fonctionnalités de base</span>
              </li>
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">Assistance par email</span>
              </li>
            </ul>
          </div>

          <Link
            href="/auth?plan=free"
            className="mt-6 inline-flex h-10 items-center justify-center rounded-full bg-primary/10 px-6 font-medium text-primary transition-colors hover:bg-primary/20 w-full"
          >
            Commencer
          </Link>
        </div>

        {/* Forfait Essentiel */}
        <div className="rounded-xl border border-white/10 backdrop-blur-md bg-card/70 p-6 flex flex-col h-full transition-all hover:bg-card/80 hover:shadow-[0_0_15px_rgba(0,214,143,0.15)]">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-1">Essentiel</h3>
            <div className="flex items-baseline gap-1">
              <span className="text-3xl font-bold">59€</span>
              <span className="text-muted-foreground text-sm">/mois</span>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Pour les indépendants et petites entreprises
            </p>
          </div>

          <div className="space-y-4 flex-grow">
            <div className="bg-muted/40 rounded-lg p-3">
              <p className="text-sm font-medium">30 missions par mois</p>
              <p className="text-xs text-muted-foreground">
                Environ 5 heures d'utilisation
              </p>
            </div>

            <ul className="space-y-2.5">
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">
                  Tout ce qui est inclus dans Découverte
                </span>
              </li>
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">Génération de contenus avancée</span>
              </li>
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">Assistance premium</span>
              </li>
            </ul>
          </div>

          <Link
            href="/auth?plan=essential"
            className="mt-6 inline-flex h-10 items-center justify-center rounded-full bg-primary px-6 font-medium text-primary-foreground transition-colors hover:bg-primary/90 w-full"
          >
            S'abonner
          </Link>
        </div>

        {/* Forfait Business */}
        <div className="rounded-xl border-2 border-primary/50 backdrop-blur-md bg-card/70 p-6 flex flex-col h-full relative transition-all hover:bg-card/80 hover:shadow-[0_0_15px_rgba(0,214,143,0.15)]">
          <div className="absolute top-0 right-6 transform -translate-y-1/2 bg-primary text-primary-foreground text-xs font-medium px-3 py-1 rounded-full">
            Populaire
          </div>
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-1">Business</h3>
            <div className="flex items-baseline gap-1">
              <span className="text-3xl font-bold">299€</span>
              <span className="text-muted-foreground text-sm">/mois</span>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Pour les PME et les équipes
            </p>
          </div>

          <div className="space-y-4 flex-grow">
            <div className="bg-muted/40 rounded-lg p-3">
              <p className="text-sm font-medium">200 missions par mois</p>
              <p className="text-xs text-muted-foreground">
                Environ 33 heures d'utilisation
              </p>
            </div>

            <ul className="space-y-2.5">
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">
                  Tout ce qui est inclus dans Essentiel
                </span>
              </li>
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">Analyse de données avancée</span>
              </li>
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">Accès aux modèles spécialisés</span>
              </li>
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">Support prioritaire</span>
              </li>
            </ul>
          </div>

          <Link
            href="/auth?plan=business"
            className="mt-6 inline-flex h-10 items-center justify-center rounded-full bg-primary px-6 font-medium text-primary-foreground transition-colors hover:bg-primary/90 w-full"
          >
            S'abonner
          </Link>
        </div>

        {/* Forfait Enterprise */}
        <div className="rounded-xl border border-white/10 backdrop-blur-md bg-card/70 p-6 flex flex-col h-full transition-all hover:bg-card/80 hover:shadow-[0_0_15px_rgba(0,214,143,0.15)]">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-1">Enterprise</h3>
            <div className="flex items-baseline gap-1">
              <span className="text-muted-foreground text-sm mr-1">
                À partir de
              </span>
              <span className="text-3xl font-bold">599€</span>
              <span className="text-muted-foreground text-sm">/mois</span>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Service complet de conciergerie IA
            </p>
          </div>

          <div className="space-y-4 flex-grow">
            <div className="bg-muted/40 rounded-lg p-3">
              <p className="text-sm font-medium">Usage illimité</p>
              <p className="text-xs text-muted-foreground">
                Assistance personnalisée
              </p>
            </div>

            <ul className="space-y-2.5">
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">
                  Tout ce qui est inclus dans Business
                </span>
              </li>
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">Prospection d'agents Orchestra</span>
              </li>
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">
                  Intégration avec vos outils (Gmail, Slack, CRM)
                </span>
              </li>
              <li className="flex items-start">
                <CheckIcon className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                <span className="text-sm">Support dédié 24/7</span>
              </li>
            </ul>
          </div>

          <Link
            href="/contact?plan=enterprise"
            className="mt-6 inline-flex h-10 items-center justify-center rounded-full bg-primary/10 px-6 font-medium text-primary transition-colors hover:bg-primary/20 w-full"
          >
            Contacter l'équipe
          </Link>
        </div>
      </div>

      <div className="w-full max-w-6xl mx-auto px-6 mt-6">
        <div className="bg-muted/30 rounded-xl p-6 border border-border">
          <h3 className="text-lg font-medium mb-3">
            Comment fonctionnent nos forfaits ?
          </h3>
          <p className="text-sm text-muted-foreground mb-4">
            Nos forfaits sont basés sur le nombre de missions, où une mission
            correspond en moyenne à 10 minutes d'utilisation d'Alex. Vous êtes
            facturé uniquement pour le temps d'utilisation réel, ce qui vous
            permet de gérer efficacement votre budget.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start">
              <div className="rounded-full bg-primary/10 p-2 mr-3 mt-0.5">
                <CheckIcon className="h-4 w-4 text-primary" />
              </div>
              <div>
                <h4 className="text-sm font-medium">Flexibilité maximale</h4>
                <p className="text-xs text-muted-foreground">
                  Passez à un forfait supérieur ou inférieur à tout moment selon
                  vos besoins
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="rounded-full bg-primary/10 p-2 mr-3 mt-0.5">
                <CheckIcon className="h-4 w-4 text-primary" />
              </div>
              <div>
                <h4 className="text-sm font-medium">Transparence totale</h4>
                <p className="text-xs text-muted-foreground">
                  Suivez facilement votre utilisation et optimisez vos coûts
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
