import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

export function PricingBanner() {
  return (
    <div className="w-full bg-gradient-to-r from-green-500/10 via-blue-500/10 to-purple-500/10 backdrop-blur-sm border-y border-border">
      <div className="max-w-7xl mx-auto py-3 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-wrap items-center justify-center sm:justify-between gap-4">
          <div className="flex items-center gap-4">
            <span className="hidden sm:inline text-sm font-medium text-primary">
              Nos tarifs
            </span>
            <div className="flex items-center space-x-2 text-sm">
              <span className="font-medium text-primary">Gratuit</span>
              <span className="text-muted-foreground">•</span>
              <span className="font-medium text-primary">59,99€/mois</span>
              <span className="text-muted-foreground">•</span>
              <span className="font-medium text-primary">299€/mois</span>
              <span className="text-muted-foreground">•</span>
              <span className="font-medium text-primary">599€/mois</span>
            </div>
          </div>
          <Link
            href="#pricing"
            className="inline-flex items-center justify-center text-xs font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring bg-primary text-primary-foreground shadow hover:bg-primary/90 h-8 rounded-full px-3"
          >
            Voir les détails
          </Link>
        </div>
      </div>
    </div>
  );
}
