'use client';

import { useState, useEffect } from 'react';
import Image, { ImageProps } from 'next/image';

interface ImageFallbackProps extends ImageProps {
  fallbackSrc?: string;
}

export default function ImageFallback({
  src,
  alt,
  fallbackSrc = '/placeholders/default.png',
  ...props
}: ImageFallbackProps) {
  const [imgSrc, setImgSrc] = useState(src);
  const [error, setError] = useState(false);

  useEffect(() => {
    setImgSrc(src);
    setError(false);
  }, [src]);

  // Fonction pour extraire le type d'image à partir du chemin
  const getImageType = (path: string): string => {
    if (path.includes('ai-pme-growth')) return '/placeholders/ai-growth.png';
    if (path.includes('process-automation'))
      return '/placeholders/automation.png';
    if (path.includes('data-security')) return '/placeholders/security.png';
    if (path.includes('data-analysis')) return '/placeholders/analysis.png';
    if (path.includes('cyber-protection')) return '/placeholders/cyber.png';
    // Image par défaut
    return '/placeholders/default.png';
  };

  return (
    <div className={`relative ${props.className || ''}`} style={props.style}>
      {/* Si l'image d'origine a une erreur, on utilise un div coloré comme placeholder */}
      {error && (
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center">
          <p className="text-primary-foreground text-sm font-medium p-2 bg-black/30 rounded backdrop-blur-sm">
            {alt || 'Image'}
          </p>
        </div>
      )}

      <Image
        {...props}
        className={`${props.className || ''} ${error ? 'opacity-0' : 'opacity-100'}`}
        src={imgSrc}
        alt={alt}
        onError={() => {
          setError(true);
        }}
      />
    </div>
  );
}
