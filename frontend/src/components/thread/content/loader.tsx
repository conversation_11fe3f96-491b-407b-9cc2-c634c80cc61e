import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AnimatedShinyText } from '@/components/ui/animated-shiny-text';

const items = [
  { id: 1, content: 'Initialisation des voies neurales...' },
  { id: 2, content: 'Analyse de la complexité de la requête...' },
  { id: 3, content: 'Assemblage du cadre cognitif...' },
  { id: 4, content: 'Orchestration des processus de réflexion...' },
  { id: 5, content: 'Synthèse de la compréhension contextuelle...' },
  { id: 6, content: 'Calibrage des paramètres de réponse...' },
  { id: 7, content: 'Activation des algorithmes de raisonnement...' },
  { id: 8, content: 'Traitement des structures sémantiques...' },
  { id: 9, content: 'Formulation de l\'approche stratégique...' },
  { id: 10, content: 'Optimisation des chemins de solution...' },
  { id: 11, content: 'Harmonisation des flux de données...' },
  { id: 12, content: 'Architecture de la réponse intelligente...' },
  { id: 13, content: 'Ajustement fin des modèles cognitifs...' },
  { id: 14, content: 'Tissage des fils narratifs...' },
  { id: 15, content: 'Cristallisation des insights...' },
  { id: 16, content: 'Préparation de l\'analyse complète...' },
];

export const AgentLoader = () => {
  const [index, setIndex] = useState(0);
  useEffect(() => {
    const id = setInterval(() => {
      setIndex((state) => {
        if (state >= items.length - 1) return 0;
        return state + 1;
      });
    }, 1500);
    return () => clearInterval(id);
  }, []);

  return (
    <div className="flex py-2 items-center w-full">
      <div>✨</div>
      <AnimatePresence>
        <motion.div
          key={items[index].id}
          initial={{ y: 20, opacity: 0, filter: 'blur(8px)' }}
          animate={{ y: 0, opacity: 1, filter: 'blur(0px)' }}
          exit={{ y: -20, opacity: 0, filter: 'blur(8px)' }}
          transition={{ ease: 'easeInOut' }}
          style={{ position: 'absolute' }}
          className="ml-7"
        >
          <AnimatedShinyText>{items[index].content}</AnimatedShinyText>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};
