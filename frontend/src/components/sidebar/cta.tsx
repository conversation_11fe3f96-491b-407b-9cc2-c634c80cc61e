import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Users, ExternalLink } from 'lucide-react';

export function CTACard() {
  return (
    <div className="rounded-xl bg-gradient-to-br from-green-50 to-green-200 dark:from-green-950/40 dark:to-green-900/40 shadow-sm border border-green-200/50 dark:border-green-800/50 p-4 transition-all">
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col">
          <span className="text-sm font-medium text-foreground">
            🏢 Entreprise
          </span>
          <span className="text-xs text-muted-foreground mt-0.5">
            Pilotage d'employés IA sur mesure
          </span>
        </div>

        <div className="text-xs text-green-800 dark:text-green-200 leading-relaxed">
          Vous souhaitez que l'on pilote pour vous des employés IA ? 
          Découvrez nos services personnalisés.
        </div>

        <div className="flex items-center">
          <Link
            href="https://orchestraconciergerie.fr"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center text-xs text-green-700 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 transition-colors font-medium"
          >
            <Users className="mr-1.5 h-3.5 w-3.5" />
            Rendez-vous sur orchestraconciergerie.fr
            <ExternalLink className="ml-1 h-3 w-3" />
          </Link>
        </div>
      </div>
    </div>
  );
}
