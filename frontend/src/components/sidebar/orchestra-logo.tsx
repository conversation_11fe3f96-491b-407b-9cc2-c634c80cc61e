'use client';

import Image from 'next/image';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

interface OrchestraLogoProps {
  size?: number;
}

export function OrchestraLogo({ size = 10 }: OrchestraLogoProps) {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // After mount, we can access the theme
  useEffect(() => {
    setMounted(true);
  }, []);

  const logoSrc = !mounted
    ? '/logo_black_Orchestra_final_2025.svg'
    : resolvedTheme === 'dark'
      ? '/logo_blanc_Orchestra_final_2025.svg'
      : '/logo_black_Orchestra_final_2025.svg';

  return (
    <Image
      src={logoSrc}
      alt="Orchestra Connect"
      width={size * 6} // Restored original multiplier
      height={size}
      className="flex-shrink-0 object-contain"
    />
  );
}
