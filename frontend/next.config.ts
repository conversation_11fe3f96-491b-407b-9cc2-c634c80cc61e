// import { withSentryConfig } from '@sentry/nextjs';
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  webpack: (config) => {
    // This rule prevents issues with pdf.js and canvas
    config.externals = [...(config.externals || []), { canvas: 'canvas' }];

    // Ensure node native modules are ignored
    config.resolve.fallback = {
      ...config.resolve.fallback,
      canvas: false,
    };

    return config;
  },
};

// Sentry désactivé pour Render - peut être réactivé plus tard si nécessaire
// if (process.env.NODE_ENV === 'production' && process.env.SENTRY_AUTH_TOKEN) {
//   nextConfig = withSentryConfig(nextConfig, {
//     org: process.env.SENTRY_ORG || 'orchestra-connect',
//     project: process.env.SENTRY_PROJECT || 'connect-frontend',
//     silent: !process.env.CI,
//     widenClientFileUpload: true,
//     tunnelRoute: '/monitoring',
//     disableLogger: true,
//     automaticVercelMonitors: false,
//   });
// }

export default nextConfig;
