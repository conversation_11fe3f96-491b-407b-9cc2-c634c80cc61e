Orchestra Connect
Copyright 2025 Orchestra Connect

This product includes software developed by Kortix AI (https://github.com/kortix-ai/suna).

Orchestra Connect is based on <PERSON><PERSON>, an open source generalist AI agent originally 
developed by Kortix AI and contributors. The original Suna project is licensed 
under the Apache License, Version 2.0.

Original Suna project:
- Repository: https://github.com/kortix-ai/suna
- Website: https://www.suna.so
- License: Apache License 2.0

Main contributors to the original Suna project:
- <PERSON> (https://x.com/adam<PERSON><PERSON><PERSON><PERSON>)
- Da<PERSON>-lequoc (https://x.com/datlqqq)  
- <PERSON><PERSON> (https://twitter.com/markokraemer)

Orchestra Connect modifications and enhancements:
- French localization and branding
- Custom pricing structure
- PME-focused features and workflows
- Integration with Orchestra Connect services

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
