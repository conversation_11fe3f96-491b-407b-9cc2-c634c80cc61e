# 🚨 SÉCURISATION URGENTE - CONNECT PROJECT

## CLÉS API EXPOSÉES - ACTION IMMÉDIATE REQUISE

### 1. RÉVOQUER CES CLÉS IMMÉDIATEMENT

#### Stripe (CRITIQUE)
- Clé exposée: `***********************************************************************************************************`
- Action: Aller sur https://dashboard.stripe.com/apikeys → Révoquer → Créer nouvelle clé

#### OpenRouter
- Clé exposée: `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`
- Action: https://openrouter.ai/keys → Révoquer → Créer nouvelle clé

#### RapidAPI
- Clé exposée: `**************************************************`
- Action: https://rapidapi.com/developer/security → Révoquer → Créer nouvelle clé

#### Tavily
- Clé exposée: `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`
- Action: https://app.tavily.com/api-keys → Révoquer → Créer nouvelle clé

#### Firecrawl
- Clé exposée: `fc-60fe43022991488e9f9d030a4d075b8b`
- Action: https://firecrawl.dev/app/api-keys → Révoquer → Créer nouvelle clé

#### Daytona
- Clé exposée: `dtn_e650ae7d7b250337033aaa01e4a78d03c11d4135aa1df5dd57045619f36accfd`
- Action: https://app.daytona.io/settings/api-keys → Révoquer → Créer nouvelle clé

### 2. NETTOYER L'HISTORIQUE GIT

```bash
# Option 1: Rebase interactif (recommandé si peu de commits)
git rebase -i HEAD~10  # Ajuster le nombre selon l'historique

# Option 2: Utiliser git-filter-repo (plus sûr)
pip install git-filter-repo
git filter-repo --invert-paths --path render.yaml --force

# Option 3: Créer un nouveau repository (plus radical)
# Sauvegarder le code actuel sans l'historique
```

### 3. CONFIGURER LES VARIABLES RENDER

#### Accéder à Render Dashboard
1. https://dashboard.render.com/
2. Sélectionner chaque service (connect-frontend, connect-backend, connect-worker)
3. Aller dans "Environment" 
4. Ajouter les variables sensibles

#### Variables par service

**Frontend (connect-frontend):**
- NEXT_PUBLIC_SUPABASE_URL
- NEXT_PUBLIC_SUPABASE_ANON_KEY
- NEXT_PUBLIC_GOOGLE_CLIENT_ID (optionnel)

**Backend (connect-backend):**
- SUPABASE_URL
- SUPABASE_ANON_KEY  
- SUPABASE_SERVICE_ROLE_KEY
- ANTHROPIC_API_KEY (nouvelle)
- OPENAI_API_KEY (nouvelle)
- OPENROUTER_API_KEY (nouvelle)
- RAPID_API_KEY (nouvelle)
- TAVILY_API_KEY (nouvelle)
- FIRECRAWL_API_KEY (nouvelle)
- DAYTONA_API_KEY (nouvelle)
- STRIPE_SECRET_KEY (nouvelle)
- STRIPE_WEBHOOK_SECRET (nouveau)
- MAILTRAP_API_TOKEN

**Worker (connect-worker):**
- Mêmes variables que le backend

### 4. VÉRIFICATION POST-SÉCURISATION

```bash
# Vérifier qu'aucune clé n'est exposée
git log --grep="sk_live" --grep="sk-or-v1" --grep="tvly-" --grep="fc-" --grep="dtn_"

# Vérifier le fichier render.yaml
grep -E "(sk_|tvly-|fc-|dtn_)" render.yaml

# Doit retourner: aucun résultat
```

### 5. REDÉPLOIEMENT

1. Commit du render.yaml sécurisé
2. Push vers GitHub
3. Redéploiement automatique sur Render
4. Vérifier les logs de déploiement
5. Tester les fonctionnalités

## CONFIGURATION REDIS CLARIFIÉE

Vous utilisez **Render Managed Redis** - c'est parfait !
- Host: `red-d197f5ffte5s73c39cjg`
- Port: `6379`
- SSL: `false`
- URL: `redis://red-d197f5ffte5s73c39cjg:6379`

Cette configuration est correcte et sécurisée.
