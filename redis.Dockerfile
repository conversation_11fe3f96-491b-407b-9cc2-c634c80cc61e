FROM redis:7-alpine

# Copy Redis configuration
COPY backend/services/docker/redis.conf /usr/local/etc/redis/redis.conf

# Create data directory
RUN mkdir -p /data

# Expose Redis port
EXPOSE 6379

# Start Redis with configuration
CMD ["redis-server", "/usr/local/etc/redis/redis.conf", "--appendonly", "yes", "--bind", "0.0.0.0", "--protected-mode", "no", "--maxmemory", "256mb", "--maxmemory-policy", "allkeys-lru"]
